{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project/Frontend/app/components/Navbar.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport logo from '../../public/logo.png';\r\n\r\nfunction Navbar() {\r\n    return (\r\n        <div className='bg-rose-500 w-full'>\r\n            <div className=\"logo\">\r\n                <Link href=\"/\">\r\n                    <div className='flex items-center'>\r\n                        <Image src={logo} alt=\"Logo\" width={50} height={50} />\r\n                        <b>Estimate Builder</b>\r\n                    </div>\r\n                </Link>\r\n            </div>\r\n            <div className=\"menu\">\r\n                <ul>\r\n                    <li>\r\n                        <Link href=\"/\">\r\n                            Home\r\n                        </Link>\r\n                    </li>\r\n                    <li>\r\n                        <Link href=\"/about\">\r\n                            About\r\n                        </Link>\r\n                    </li>\r\n                    <li>\r\n                        <Link href=\"/contact\">\r\n                            Contact\r\n                        </Link>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <div className=\"account\">\r\n                <ul>\r\n                    <li>\r\n                        <Link href=\"/login\">\r\n                            Login\r\n                        </Link>\r\n                    </li>\r\n                    <li>\r\n                        <Link href=\"/signup\">\r\n                            Signup\r\n                        </Link>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default Navbar"], "names": [], "mappings": ";;;;AACA;AACA;;;;;;;;;;AAGA,SAAS;IACL,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACP,cAAA,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,6HAAA,CAAA,UAAK;gCAAC,KAAK;gCAAM,KAAI;gCAAO,OAAO;gCAAI,QAAQ;;;;;;0CAChD,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAIf,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;;sCACG,8OAAC;sCACG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAI;;;;;;;;;;;sCAInB,8OAAC;sCACG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAS;;;;;;;;;;;sCAIxB,8OAAC;sCACG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAW;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;;sCACG,8OAAC;sCACG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAS;;;;;;;;;;;sCAIxB,8OAAC;sCACG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;uCAEe", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project/Frontend/app/components/Footer.tsx"], "sourcesContent": ["import React from 'react'\r\n\r\nfunction Footer() {\r\n  return (\r\n    <div className='bg-emerald-500 w-full absolute bottom-0'>Footer</div>\r\n  )\r\n}\r\n\r\nexport default Footer"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;kBAA0C;;;;;;AAE7D;uCAEe", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project/Frontend/app/pages/Main.tsx"], "sourcesContent": ["import React from 'react'\r\n\r\nfunction Main() {\r\n  return (\r\n    <div>Main</div>\r\n  )\r\n}\r\n\r\nexport default Main"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;kBAAI;;;;;;AAET;uCAEe", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project/Frontend/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Navbar from \"./components/Navbar\";\nimport Footer from \"./components/Footer\";\nimport Main from \"./pages/Main\";\n\nexport default function Home() {\n  return (\n    <>\n    <div>\n      <Navbar />\n      <Main />\n      <Footer />\n    </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE;kBACA,cAAA,8OAAC;;8BACC,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC,qHAAA,CAAA,UAAI;;;;;8BACL,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;;AAIb", "debugId": null}}]}