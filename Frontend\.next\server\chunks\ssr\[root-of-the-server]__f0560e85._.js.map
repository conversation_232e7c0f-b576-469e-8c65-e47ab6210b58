{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project/Frontend/app/components/images/logo.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 1024, height: 1024, blurWidth: 8, blurHeight: 8, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+AGRkZABkZGQAa2trAHl5eQ2UlJQVh4eHAXZ2dgBubm8AAFlYWQBSUlIAWlpaG3JxcX6HhoaWkpGRb4aGhgxubm4AAFhYWABNTU0mZWRlmXFwcHZ1dHSci4qJ35qamVmCgoICAFpaWgBHR0c7Z2Zmo1xbXIReXFz+W1pa/2ppacWEg4QYAFtbWwpUVFR8aGZm2llYWLRMS0v/T05O/2BfX7JycnIMAEtLSydSUVHdW1pZ/VRTU/tBQED/S0lK/1dWVqFkZGQDADs7OyNHRkW0SkhIwklISLdKSUmsS0lJn1BQUF1aWloCAEBAQAQ2NjYQNjY2Djo6OgpAPz8GQ0NDA1BPTwFdXV0AA0tebCNIeHYAAAAASUVORK5CYII=\"};\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,4HAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,WAAW;IAAG,YAAY;IAAG,aAAa;AAAod", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project/Frontend/app/components/Navbar.tsx"], "sourcesContent": ["import React from 'react'\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport logo from '../components/images/logo.png';\r\n\r\nfunction Navbar() {\r\n    return (\r\n        <div className='bg-rose-500 w-full'>\r\n            <div className=\"logo\">\r\n                <Link href=\"/\">\r\n                    <div className='flex items-center'>\r\n                        <Image src={logo} alt=\"Logo\" width={50} height={50} />\r\n                        <b>Estimate Builder</b>\r\n                    </div>\r\n                </Link>\r\n            </div>\r\n            Navbar\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default Navbar"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,SAAS;IACL,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACP,cAAA,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,6HAAA,CAAA,UAAK;gCAAC,KAAK,sSAAA,CAAA,UAAI;gCAAE,KAAI;gCAAO,OAAO;gCAAI,QAAQ;;;;;;0CAChD,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;YAGT;;;;;;;AAIlB;uCAEe", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project/Frontend/app/components/Footer.tsx"], "sourcesContent": ["import React from 'react'\r\n\r\nfunction Footer() {\r\n  return (\r\n    <div className='bg-emerald-500 w-full absolute bottom-0'>Footer</div>\r\n  )\r\n}\r\n\r\nexport default Footer"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;kBAA0C;;;;;;AAE7D;uCAEe", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project/Frontend/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Navbar from \"./components/Navbar\";\nimport Footer from \"./components/Footer\";\nimport Main from \"./components/Main\";\n\nexport default function Home() {\n  return (\n    <>\n    <div>\n      <Navbar />\n      <Main />\n      <Footer />\n    </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;;;;;;;AAGe,SAAS;IACtB,qBACE;kBACA,cAAA,8OAAC;;8BACC,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;;;;;8BACD,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;;AAIb", "debugId": null}}]}